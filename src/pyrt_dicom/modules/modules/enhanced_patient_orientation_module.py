"""Enhanced Patient Orientation Module - DICOM PS3.3 C.7.6.30"""
from .base_module import BaseModule
from ...validators.modules.enhanced_patient_orientation_validator import EnhancedPatientOrientationValidator
from ...validators.modules.base_validator import ValidationConfig
from ...validators import ValidationResult
from pydicom import Dataset


class EnhancedPatientOrientationModule(BaseModule):
    """Enhanced Patient Orientation Module implementation for DICOM PS3.3 C.7.6.30.

    Uses composition with internal dataset management rather than inheriting from
    pydicom.Dataset for cleaner separation of concerns. Describes the patient
    orientation with respect to gravity and to the equipment using three Attributes
    invoked from the Patient Orientation and Equipment Relationship Macro Attributes.

    This module includes "Table 10-15a Patient Orientation and Equipment Relationship
    Macro Attributes" as specified in DICOM PS3.3. The macro inclusion provides
    additional attributes beyond the three core sequences and is planned for future
    implementation.

    Usage:
        # Create with required elements
        orientation = EnhancedPatientOrientationModule.from_required_elements(
            patient_orientation_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102538003",
                    coding_scheme_designator="SCT",
                    code_meaning="recumbent"
                )
            ],
            patient_orientation_modifier_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="40199007",
                    coding_scheme_designator="SCT",
                    code_meaning="supine"
                )
            ],
            patient_equipment_relationship_code_sequence=[
                EnhancedPatientOrientationModule.create_code_sequence_item(
                    code_value="102540008",
                    coding_scheme_designator="SCT",
                    code_meaning="headfirst"
                )
            ]
        )

        # Generate dataset for IOD integration
        dataset = orientation.to_dataset()

        # Validate including cross-sequence consistency
        result = orientation.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        patient_orientation_code_sequence: list[Dataset],
        patient_orientation_modifier_code_sequence: list[Dataset],
        patient_equipment_relationship_code_sequence: list[Dataset]
    ) -> 'EnhancedPatientOrientationModule':
        """Create Enhanced Patient Orientation Module from all required (Type 1) data elements.

        Args:
            patient_orientation_code_sequence (list[Dataset]): Rough orientation of imaged part
                with respect to gravity (0054,0410) Type 1. Describes vertical, horizontal,
                or in-between positioning.
            patient_orientation_modifier_code_sequence (list[Dataset]): Detailed description
                of orientation and positioning (0054,0412) Type 1. Provides more specific
                positioning information.
            patient_equipment_relationship_code_sequence (list[Dataset]): Orientation of patient
                with respect to imaging equipment (3010,0030) Type 1. Describes relationship
                to gantry/equipment.

        Returns:
            EnhancedPatientOrientationModule: New module instance with required data elements set
        """
        instance = cls()
        instance._dataset.PatientOrientationCodeSequence = patient_orientation_code_sequence
        instance._dataset.PatientOrientationModifierCodeSequence = patient_orientation_modifier_code_sequence
        instance._dataset.PatientEquipmentRelationshipCodeSequence = patient_equipment_relationship_code_sequence
        return instance
    
    def with_optional_elements(self, **kwargs) -> 'EnhancedPatientOrientationModule':
        """Add optional (Type 3) data elements to the module instance.
        
        The Enhanced Patient Orientation Module has no Type 3 elements defined in DICOM PS3.3 C.7.6.30.
        This method is provided for API consistency but accepts no parameters.
        
        Args:
            **kwargs: No optional elements are supported
            
        Returns:
            EnhancedPatientOrientationModule: Self for method chaining
            
        Raises:
            ValueError: If any keyword arguments are provided
        """
        if kwargs:
            raise ValueError(f"EnhancedPatientOrientationModule has no optional elements. Unexpected arguments: {list(kwargs.keys())}")
        return self
    
    @staticmethod
    def create_code_sequence_item(
        code_value: str,
        coding_scheme_designator: str,
        code_meaning: str,
        coding_scheme_version: str | None = None,
        context_identifier: str | None = None,
        context_uid: str | None = None,
        mapping_resource: str | None = None,
        context_group_version: str | None = None,
        context_group_extension_flag: str | None = None,
        context_group_local_version: str | None = None,
        context_group_extension_creator_uid: str | None = None
    ) -> Dataset:
        """Create code sequence item for orientation code sequences.
        
        Args:
            code_value (str): Code Value (0008,0100) Type 1
            coding_scheme_designator (str): Coding Scheme Designator (0008,0102) Type 1
            code_meaning (str): Code Meaning (0008,0104) Type 1
            coding_scheme_version (str | None): Coding Scheme Version (0008,0103) Type 1C
            context_identifier (str | None): Context Identifier (0008,010F) Type 3
            context_uid (str | None): Context UID (0008,0117) Type 3
            mapping_resource (str | None): Mapping Resource (0008,0105) Type 1C
            context_group_version (str | None): Context Group Version (0008,0106) Type 1C
            context_group_extension_flag (str | None): Context Group Extension Flag (0008,010B) Type 3
            context_group_local_version (str | None): Context Group Local Version (0008,0107) Type 1C
            context_group_extension_creator_uid (str | None): Context Group Extension Creator UID (0008,010D) Type 1C
            
        Returns:
            Dataset: Code sequence item with required and optional attributes
        """
        item = Dataset()
        item.CodeValue = code_value
        item.CodingSchemeDesignator = coding_scheme_designator
        item.CodeMeaning = code_meaning
        
        if coding_scheme_version is not None:
            item.CodingSchemeVersion = coding_scheme_version
        if context_identifier is not None:
            item.ContextIdentifier = context_identifier
        if context_uid is not None:
            item.ContextUID = context_uid
        if mapping_resource is not None:
            item.MappingResource = mapping_resource
        if context_group_version is not None:
            item.ContextGroupVersion = context_group_version
        if context_group_extension_flag is not None:
            item.ContextGroupExtensionFlag = context_group_extension_flag
        if context_group_local_version is not None:
            item.ContextGroupLocalVersion = context_group_local_version
        if context_group_extension_creator_uid is not None:
            item.ContextGroupExtensionCreatorUID = context_group_extension_creator_uid
            
        return item
    
    @property
    def has_orientation_data(self) -> bool:
        """Check if orientation data is present.

        Returns:
            bool: True if all required orientation sequences are present
        """
        return (hasattr(self._dataset, 'PatientOrientationCodeSequence') and
                hasattr(self._dataset, 'PatientOrientationModifierCodeSequence') and
                hasattr(self._dataset, 'PatientEquipmentRelationshipCodeSequence'))

    @property
    def is_recumbent(self) -> bool:
        """Check if patient is in recumbent position.

        Returns:
            bool: True if patient orientation indicates recumbent position
        """
        if not hasattr(self._dataset, 'PatientOrientationCodeSequence'):
            return False

        for item in self._dataset.PatientOrientationCodeSequence:
            if (item.get('CodeValue') == '102538003' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False

    @property
    def is_erect(self) -> bool:
        """Check if patient is in erect position.

        Returns:
            bool: True if patient orientation indicates erect position
        """
        if not hasattr(self._dataset, 'PatientOrientationCodeSequence'):
            return False

        for item in self._dataset.PatientOrientationCodeSequence:
            if (item.get('CodeValue') == 'C86043' and
                item.get('CodingSchemeDesignator') == 'NCIt'):
                return True
        return False

    @property
    def is_headfirst(self) -> bool:
        """Check if patient is positioned headfirst.

        Returns:
            bool: True if patient equipment relationship indicates headfirst
        """
        if not hasattr(self._dataset, 'PatientEquipmentRelationshipCodeSequence'):
            return False

        for item in self._dataset.PatientEquipmentRelationshipCodeSequence:
            if (item.get('CodeValue') == '102540008' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False

    @property
    def is_feetfirst(self) -> bool:
        """Check if patient is positioned feetfirst.

        Returns:
            bool: True if patient equipment relationship indicates feetfirst
        """
        if not hasattr(self._dataset, 'PatientEquipmentRelationshipCodeSequence'):
            return False

        for item in self._dataset.PatientEquipmentRelationshipCodeSequence:
            if (item.get('CodeValue') == '102541007' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False

    @property
    def is_semi_erect(self) -> bool:
        """Check if patient is in semi-erect position.

        Returns:
            bool: True if patient orientation indicates semi-erect position
        """
        if not hasattr(self._dataset, 'PatientOrientationCodeSequence'):
            return False

        for item in self._dataset.PatientOrientationCodeSequence:
            if (item.get('CodeValue') == '102539006' and
                item.get('CodingSchemeDesignator') == 'SCT'):
                return True
        return False
    
    @property
    def is_consistent_combination(self) -> bool:
        """Check if orientation, modifier, and equipment sequences are logically consistent.

        Returns:
            bool: True if the combination of orientation, modifier, and equipment
                  relationship codes represents a valid logical combination
        """
        if not self.has_orientation_data:
            return False

        # Get primary orientation codes
        orientation_codes = [item.get('CodeValue', '') for item in self._dataset.PatientOrientationCodeSequence]
        modifier_codes = [item.get('CodeValue', '') for item in self._dataset.PatientOrientationModifierCodeSequence]

        # Check for mutually exclusive orientations
        if ('102538003' in orientation_codes and 'C86043' in orientation_codes):  # recumbent and erect
            return False
        if ('102538003' in orientation_codes and '102539006' in orientation_codes):  # recumbent and semi-erect
            return False
        if ('C86043' in orientation_codes and '102539006' in orientation_codes):  # erect and semi-erect
            return False

        # Check orientation-modifier compatibility
        if '102538003' in orientation_codes:  # recumbent
            # Should have supine, prone, or similar recumbent modifiers
            valid_modifiers = ['40199007', '1240000']  # supine, prone
            if not any(code in valid_modifiers for code in modifier_codes):
                return False

        if 'C86043' in orientation_codes:  # erect
            # Should have standing, sitting, or similar erect modifiers
            valid_modifiers = ['10904000', '33586001']  # standing, sitting
            if not any(code in valid_modifiers for code in modifier_codes):
                return False

        if '102539006' in orientation_codes:  # semi-erect
            # Semi-erect typically pairs with sitting or other intermediate positions
            valid_modifiers = ['33586001']  # sitting is most common for semi-erect
            if not any(code in valid_modifiers for code in modifier_codes):
                # This is more of a warning than a hard failure for semi-erect
                pass

        return True

    def _get_sequence_codes(self, sequence_attr: str) -> list[str]:
        """Helper method to extract code values from a sequence attribute.

        Args:
            sequence_attr (str): Name of the sequence attribute

        Returns:
            list[str]: List of code values from the sequence
        """
        if not hasattr(self._dataset, sequence_attr):
            return []

        sequence = getattr(self._dataset, sequence_attr, [])
        return [item.get('CodeValue', '') for item in sequence if item.get('CodeValue')]

    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Enhanced Patient Orientation Module data against DICOM standard.

        Args:
            config (ValidationConfig | None): Validation configuration options

        Returns:
            ValidationResult: Validation results with 'errors' and 'warnings' lists
        """
        return EnhancedPatientOrientationValidator.validate(self._dataset, config)
